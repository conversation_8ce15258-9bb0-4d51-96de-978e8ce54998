# mTicket.my - Event Management Platform

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/nqmns-projects/v0-m-ticketz)
[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js%2015-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![Database](https://img.shields.io/badge/Database-Supabase-green?style=for-the-badge&logo=supabase)](https://supabase.com/)
[![Security](https://img.shields.io/badge/Security-RLS%20Enabled-blue?style=for-the-badge&logo=shield)](https://supabase.com/docs/guides/auth/row-level-security)

## Overview

mTicket.my is a comprehensive event management platform built with Next.js 15, Supabase, and TypeScript. The platform provides a complete solution for event organizers to create, manage, and monetize events, while offering attendees a seamless registration and certificate experience.

## ✨ Key Features

### 🎯 Event Management
- **Complete Event Lifecycle**: Create, manage, publish, and track events
- **Rich Event Details**: Images, descriptions, categories, and scheduling
- **Multiple Event Types**: Conferences, workshops, webinars, races, and more
- **QR Code Generation**: Easy event sharing and promotion
- **Attendance Tracking**: QR code-based check-in system

### 👥 User Management & Authentication
- **Secure Authentication**: JWT-based authentication with Supabase
- **Role-Based Access Control**: Granular permissions (Admin, Manager, User, Event Admin)
- **Organization Management**: Multi-tenant support for event organizers
- **User Profiles**: Comprehensive user management with activity tracking

### 💳 Subscription & Payment System
- **Tiered Subscription Plans**: Free, Pro, and Enterprise plans
- **Feature-Based Limitations**: Events, attendees, certificates per plan
- **Multiple Payment Gateways**: Flexible payment processing integration
- **Revenue Tracking**: Comprehensive financial analytics

### 🏆 Certificate System
- **Automated Certificate Generation**: Custom templates with drag-drop fields
- **QR Code Verification**: Blockchain-style authenticity verification
- **Multiple Templates**: Professional certificate designs
- **Bulk Generation**: Mass certificate creation for events

### 📊 Analytics & Reporting
- **Event Performance Metrics**: Registration, attendance, and revenue tracking
- **User Engagement Analytics**: Activity monitoring and insights
- **Subscription Analytics**: Usage statistics and billing reports
- **Activity Logging**: Comprehensive audit trail for all actions

### 🔗 Integration & API
- **Webhook System**: Real-time event notifications
- **RESTful API**: Complete API for third-party integrations
- **Activity Logging**: Detailed action tracking across the platform

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 18 with TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Hooks + Context API
- **Analytics**: Vercel Analytics + Speed Insights

### Backend & Database
- **Database**: PostgreSQL with Supabase
- **Authentication**: Custom JWT implementation
- **API**: Next.js API Routes with TypeScript
- **File Storage**: Supabase Storage
- **Security**: ✅ Row Level Security (RLS) on all 24 tables

### Development & Deployment
- **Package Manager**: pnpm
- **Deployment**: Vercel with automatic deployments
- **Version Control**: Git with feature branch workflow
- **Code Quality**: ESLint + Prettier + TypeScript

## 📁 Project Structure

```
mTicket.my/
├── app/                    # Next.js 15 App Router
│   ├── (auth)/            # Authentication pages
│   ├── dashboard/         # Protected dashboard pages
│   ├── events/           # Event-related pages
│   ├── api/              # API routes
│   └── globals.css       # Global styles
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   ├── forms/           # Form components
│   └── layout/          # Layout components
├── contexts/            # React context providers
├── hooks/               # Custom React hooks
├── lib/                 # Utility functions and configurations
├── public/              # Static assets
├── docs/                # 📚 Comprehensive documentation
│   ├── README.md        # Documentation index
│   ├── getting-started/ # Installation and setup guides
│   ├── architecture/    # System architecture and design
│   ├── features/        # Feature documentation
│   ├── testing/         # Testing procedures and results
│   ├── security/        # Security implementation
│   ├── guides/          # User and admin guides
│   ├── api/             # API documentation
│   ├── deployment/      # Deployment guides
│   └── migrations/      # Database migration docs
└── supabase/            # Database migrations and types
```

## 🚀 Getting Started

### Prerequisites

- **Node.js**: 18.x or higher
- **Package Manager**: pnpm (recommended) or npm
- **Database**: Supabase account and project

### Installation

```bash
# Clone the repository
git clone https://github.com/nqmn/mticket.my.git
cd mticket.my

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Run database migrations (if needed)
pnpm db:migrate

# Start the development server
pnpm dev
```

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000

# Payment Gateways (Optional)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Email Service (Optional)
RESEND_API_KEY=your-resend-api-key
```

## 🌐 Deployment

### Production Deployment
The project is automatically deployed to Vercel when changes are pushed to the main branch.

**Live deployment**: [https://mticket.my](https://mticket.my)

### Deployment Configuration
- **Platform**: Vercel with automatic deployments
- **Database**: Supabase PostgreSQL with global distribution
- **CDN**: Vercel Edge Network for optimal performance
- **Analytics**: Vercel Analytics and Speed Insights integrated

## 🔒 Security Features

- ✅ **Row Level Security (RLS)**: All 24 database tables protected
- 🔐 **JWT Authentication**: Secure token-based authentication
- 🛡️ **Role-Based Access Control**: Granular permission system
- 🔍 **Input Validation**: Comprehensive data validation
- 📝 **Activity Logging**: Complete audit trail
- 🚫 **CORS Protection**: Proper API security configuration

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

- **[Documentation Index](./docs/README.md)** - Complete documentation overview with organized structure
- **[Getting Started](./docs/getting-started/)** - Installation and quick start guides
- **[Architecture](./docs/architecture/)** - System design and technical architecture
- **[Features](./docs/features/)** - Detailed feature documentation and implementation guides
- **[Testing](./docs/testing/)** - Testing procedures, checklists, and results
- **[Security](./docs/security/)** - Security implementation and best practices
- **[API Reference](./docs/api/)** - Complete API documentation and integration guides
- **[User Guides](./docs/guides/)** - User, admin, and developer guides
- **[Deployment](./docs/deployment/)** - Production deployment and maintenance guides

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Update documentation** as needed
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Development Guidelines
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write tests for new features
- Update documentation for any changes
- Follow the established patterns and conventions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js 15](https://nextjs.org/) and [React 18](https://react.dev/)
- Database and authentication powered by [Supabase](https://supabase.com/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Deployed on [Vercel](https://vercel.com/)

---

**mTicket.my** - Empowering event organizers with comprehensive event management solutions.